<?php
// Minimal bot for testing
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

header('Content-Type: application/json');
http_response_code(200);

// Configuration
$botToken = "7891979058:AAFpd9OaLt8lN9WkGURVR67YQ8XKy0A9hqo";
$apiURL = "https://api.telegram.org/bot$botToken/";
$reactions = ['👍', '❤️', '🔥', '🥰', '👏'];

// Simple sendMessage function
function sendMessage($chat_id, $text) {
    global $apiURL;
    
    $url = $apiURL . "sendMessage";
    $post_fields = [
        "chat_id" => $chat_id,
        "text" => $text,
        "parse_mode" => "Markdown"
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_fields));
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $result = curl_exec($ch);
    if (curl_errno($ch)) {
        error_log('Curl error: ' . curl_error($ch));
    }
    curl_close($ch);
    return $result;
}

// Simple setReaction function
function setReaction($chat_id, $message_id, $emoji) {
    global $apiURL;
    
    $url = $apiURL . "setMessageReaction";
    $post_fields = json_encode([
        "chat_id" => $chat_id,
        "message_id" => $message_id,
        "reaction" => [
            [
                "type" => "emoji",
                "emoji" => $emoji
            ]
        ],
        "is_big" => true
    ]);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ["Content-Type: application/json"]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $result = curl_exec($ch);
    if (curl_errno($ch)) {
        error_log('Curl error in setReaction: ' . curl_error($ch));
    }
    curl_close($ch);
    return $result;
}

// Process incoming updates
try {
    $input = file_get_contents("php://input");
    if (empty($input)) {
        echo json_encode(["ok" => true]);
        exit;
    }
    
    $update = json_decode($input, true);
    if (!$update || json_last_error() !== JSON_ERROR_NONE) {
        error_log("Invalid JSON: " . json_last_error_msg());
        echo json_encode(["ok" => true]);
        exit;
    }
    
    // Log the update for debugging
    error_log("Received update: " . json_encode($update));
    
    // Handle channel posts
    if (isset($update["channel_post"])) {
        $post = $update["channel_post"];
        $chat_id = $post["chat"]["id"];
        $message_id = $post["message_id"];
        
        // Add random reaction
        $randomEmoji = $reactions[array_rand($reactions)];
        setReaction($chat_id, $message_id, $randomEmoji);
        
        error_log("Added reaction $randomEmoji to message $message_id in chat $chat_id");
    }
    
    // Handle regular messages
    if (isset($update["message"])) {
        $message = $update["message"];
        $chat_id = $message["chat"]["id"];
        $message_id = $message["message_id"];
        $text = isset($message["text"]) ? $message["text"] : "";
        
        // Handle /start command
        if (strpos($text, "/start") === 0) {
            sendMessage($chat_id, "🤖 *Hello! I'm Auto Reaction Bot!*\n\nI add automatic reactions to your messages!");
        }
        
        // Add reaction to all messages
        $randomEmoji = $reactions[array_rand($reactions)];
        setReaction($chat_id, $message_id, $randomEmoji);
        
        error_log("Added reaction $randomEmoji to message $message_id in chat $chat_id");
    }
    
} catch (Exception $e) {
    error_log("Error processing update: " . $e->getMessage());
}

// Send success response
echo json_encode(["ok" => true]);
?>

<?php
// Check file permissions and PHP configuration
header('Content-Type: text/plain');

echo "=== PHP Configuration Check ===\n";
echo "PHP Version: " . phpversion() . "\n";
echo "Error Reporting: " . error_reporting() . "\n";
echo "Display Errors: " . ini_get('display_errors') . "\n";
echo "Log Errors: " . ini_get('log_errors') . "\n";
echo "Error Log: " . ini_get('error_log') . "\n";

echo "\n=== File Permissions Check ===\n";

$files = ['bot.php', 'channel.json', 'usersdata.json', 'image.jpg'];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "$file: EXISTS\n";
        echo "  Readable: " . (is_readable($file) ? 'YES' : 'NO') . "\n";
        echo "  Writable: " . (is_writable($file) ? 'YES' : 'NO') . "\n";
        echo "  Permissions: " . substr(sprintf('%o', fileperms($file)), -4) . "\n";
        echo "  Size: " . filesize($file) . " bytes\n";
    } else {
        echo "$file: NOT FOUND\n";
    }
    echo "\n";
}

echo "=== Directory Permissions ===\n";
$dir = '.';
echo "Directory: $dir\n";
echo "Readable: " . (is_readable($dir) ? 'YES' : 'NO') . "\n";
echo "Writable: " . (is_writable($dir) ? 'YES' : 'NO') . "\n";
echo "Permissions: " . substr(sprintf('%o', fileperms($dir)), -4) . "\n";

echo "\n=== Test File Operations ===\n";

// Test writing to channel.json
try {
    $testData = ['test' => 'data', 'timestamp' => time()];
    $result = file_put_contents('test_write.json', json_encode($testData));
    if ($result !== false) {
        echo "Write test: SUCCESS ($result bytes written)\n";
        unlink('test_write.json');
    } else {
        echo "Write test: FAILED\n";
    }
} catch (Exception $e) {
    echo "Write test: ERROR - " . $e->getMessage() . "\n";
}

// Test cURL
echo "\n=== cURL Test ===\n";
if (function_exists('curl_init')) {
    echo "cURL: AVAILABLE\n";
    $ch = curl_init();
    if ($ch) {
        echo "cURL init: SUCCESS\n";
        curl_close($ch);
    } else {
        echo "cURL init: FAILED\n";
    }
} else {
    echo "cURL: NOT AVAILABLE\n";
}

// Test file_get_contents with URL
echo "\n=== URL Access Test ===\n";
if (ini_get('allow_url_fopen')) {
    echo "allow_url_fopen: ENABLED\n";
} else {
    echo "allow_url_fopen: DISABLED\n";
}

echo "\n=== Memory and Execution ===\n";
echo "Memory Limit: " . ini_get('memory_limit') . "\n";
echo "Max Execution Time: " . ini_get('max_execution_time') . "\n";
echo "Current Memory Usage: " . memory_get_usage(true) . " bytes\n";
?>

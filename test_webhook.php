<?php
// Simple webhook test script
header('Content-Type: application/json');
http_response_code(200);

// Log the request
error_log("Webhook test accessed at " . date('Y-m-d H:i:s'));

// Get input
$input = file_get_contents("php://input");
error_log("Received input: " . $input);

// Try to decode JSON
if (!empty($input)) {
    $data = json_decode($input, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        error_log("Valid JSON received with keys: " . implode(', ', array_keys($data)));
    } else {
        error_log("Invalid JSON: " . json_last_error_msg());
    }
}

// Return success response
echo json_encode([
    "ok" => true,
    "message" => "Webhook test successful",
    "timestamp" => time()
]);
?>
